{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/auth/AuthForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { createSupabaseClient } from '@/lib/supabase'\n\nconst loginSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters')\n})\n\nconst registerSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  email: z.string().email('Invalid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n  confirmPassword: z.string().min(6, 'Password confirmation is required')\n}).refine((data) => data.password === data.confirmPassword, {\n  message: \"Passwords don't match\",\n  path: [\"confirmPassword\"]\n})\n\ntype LoginFormData = z.infer<typeof loginSchema>\ntype RegisterFormData = z.infer<typeof registerSchema>\n\nexport default function AuthForm() {\n  const [isLogin, setIsLogin] = useState(true)\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  const supabase = createSupabaseClient()\n\n  const loginForm = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema)\n  })\n\n  const registerForm = useForm<RegisterFormData>({\n    resolver: zodResolver(registerSchema)\n  })\n\n  const onLogin = async (data: LoginFormData) => {\n    setLoading(true)\n    setMessage('')\n\n    try {\n      const { error } = await supabase.auth.signInWithPassword({\n        email: data.email,\n        password: data.password\n      })\n\n      if (error) {\n        setMessage(error.message)\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const onRegister = async (data: RegisterFormData) => {\n    setLoading(true)\n    setMessage('')\n\n    try {\n      const { error } = await supabase.auth.signUp({\n        email: data.email,\n        password: data.password,\n        options: {\n          data: {\n            name: data.name,\n            role: 'worker' // Default role for self-registration\n          }\n        }\n      })\n\n      if (error) {\n        setMessage(error.message)\n      } else {\n        setMessage('Registration successful! Please check your email to verify your account.')\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Scaffolding Management\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            {isLogin ? 'Sign in to your account' : 'Create a new account'}\n          </p>\n        </div>\n\n        {/* Toggle between Login and Register */}\n        <div className=\"flex rounded-md shadow-sm\">\n          <button\n            type=\"button\"\n            onClick={() => setIsLogin(true)}\n            className={`flex-1 py-2 px-4 text-sm font-medium rounded-l-md border ${\n              isLogin\n                ? 'bg-blue-600 text-white border-blue-600'\n                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'\n            }`}\n          >\n            Login\n          </button>\n          <button\n            type=\"button\"\n            onClick={() => setIsLogin(false)}\n            className={`flex-1 py-2 px-4 text-sm font-medium rounded-r-md border-t border-r border-b ${\n              !isLogin\n                ? 'bg-blue-600 text-white border-blue-600'\n                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'\n            }`}\n          >\n            Register\n          </button>\n        </div>\n\n        {isLogin ? (\n          <form className=\"mt-8 space-y-6\" onSubmit={loginForm.handleSubmit(onLogin)}>\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                  Email address\n                </label>\n                <input\n                  {...loginForm.register('email')}\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Email address\"\n                />\n                {loginForm.formState.errors.email && (\n                  <p className=\"mt-1 text-sm text-red-600\">{loginForm.formState.errors.email.message}</p>\n                )}\n              </div>\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                  Password\n                </label>\n                <input\n                  {...loginForm.register('password')}\n                  type=\"password\"\n                  autoComplete=\"current-password\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Password\"\n                />\n                {loginForm.formState.errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{loginForm.formState.errors.password.message}</p>\n                )}\n              </div>\n            </div>\n\n            {message && (\n              <div className={`text-sm text-center p-3 rounded-md ${\n                message.includes('successful') \n                  ? 'bg-green-50 text-green-700 border border-green-200' \n                  : 'bg-red-50 text-red-700 border border-red-200'\n              }`}>\n                {message}\n              </div>\n            )}\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Signing in...' : 'Sign in'}\n              </button>\n            </div>\n          </form>\n        ) : (\n          <form className=\"mt-8 space-y-6\" onSubmit={registerForm.handleSubmit(onRegister)}>\n            <div className=\"space-y-4\">\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">\n                  Full Name\n                </label>\n                <input\n                  {...registerForm.register('name')}\n                  type=\"text\"\n                  autoComplete=\"name\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Full name\"\n                />\n                {registerForm.formState.errors.name && (\n                  <p className=\"mt-1 text-sm text-red-600\">{registerForm.formState.errors.name.message}</p>\n                )}\n              </div>\n              <div>\n                <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                  Email address\n                </label>\n                <input\n                  {...registerForm.register('email')}\n                  type=\"email\"\n                  autoComplete=\"email\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Email address\"\n                />\n                {registerForm.formState.errors.email && (\n                  <p className=\"mt-1 text-sm text-red-600\">{registerForm.formState.errors.email.message}</p>\n                )}\n              </div>\n              <div>\n                <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                  Password\n                </label>\n                <input\n                  {...registerForm.register('password')}\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Password\"\n                />\n                {registerForm.formState.errors.password && (\n                  <p className=\"mt-1 text-sm text-red-600\">{registerForm.formState.errors.password.message}</p>\n                )}\n              </div>\n              <div>\n                <label htmlFor=\"confirmPassword\" className=\"block text-sm font-medium text-gray-700\">\n                  Confirm Password\n                </label>\n                <input\n                  {...registerForm.register('confirmPassword')}\n                  type=\"password\"\n                  autoComplete=\"new-password\"\n                  className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                  placeholder=\"Confirm password\"\n                />\n                {registerForm.formState.errors.confirmPassword && (\n                  <p className=\"mt-1 text-sm text-red-600\">{registerForm.formState.errors.confirmPassword.message}</p>\n                )}\n              </div>\n            </div>\n\n            {message && (\n              <div className={`text-sm text-center p-3 rounded-md ${\n                message.includes('successful') \n                  ? 'bg-green-50 text-green-700 border border-green-200' \n                  : 'bg-red-50 text-red-700 border border-red-200'\n              }`}>\n                {message}\n              </div>\n            )}\n\n            <div>\n              <button\n                type=\"submit\"\n                disabled={loading}\n                className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                {loading ? 'Creating account...' : 'Create account'}\n              </button>\n            </div>\n          </form>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQA,MAAM,cAAc,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEA,MAAM,iBAAiB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAKe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,YAAY,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACvC,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,eAAe,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAoB;QAC7C,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,UAAU,OAAO;QACrB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;gBACvD,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;YACzB;YAEA,IAAI,OAAO;gBACT,WAAW,MAAM,OAAO;YAC1B;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBAC3C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,SAAS;oBACP,MAAM;wBACJ,MAAM,KAAK,IAAI;wBACf,MAAM,SAAS,qCAAqC;oBACtD;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,WAAW,MAAM,OAAO;YAC1B,OAAO;gBACL,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;sCACV,UAAU,4BAA4B;;;;;;;;;;;;8BAK3C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,yDAAyD,EACnE,UACI,2CACA,2DACJ;sCACH;;;;;;sCAGD,6LAAC;4BACC,MAAK;4BACL,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,6EAA6E,EACvF,CAAC,UACG,2CACA,2DACJ;sCACH;;;;;;;;;;;;gBAKF,wBACC,6LAAC;oBAAK,WAAU;oBAAiB,UAAU,UAAU,YAAY,CAAC;;sCAChE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACE,GAAG,UAAU,QAAQ,CAAC,QAAQ;4CAC/B,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,UAAU,SAAS,CAAC,MAAM,CAAC,KAAK,kBAC/B,6LAAC;4CAAE,WAAU;sDAA6B,UAAU,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAGtF,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACE,GAAG,UAAU,QAAQ,CAAC,WAAW;4CAClC,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,UAAU,SAAS,CAAC,MAAM,CAAC,QAAQ,kBAClC,6LAAC;4CAAE,WAAU;sDAA6B,UAAU,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO;;;;;;;;;;;;;;;;;;wBAK1F,yBACC,6LAAC;4BAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,QAAQ,CAAC,gBACb,uDACA,gDACJ;sCACC;;;;;;sCAIL,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;yCAKnC,6LAAC;oBAAK,WAAU;oBAAiB,UAAU,aAAa,YAAY,CAAC;;sCACnE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA0C;;;;;;sDAG1E,6LAAC;4CACE,GAAG,aAAa,QAAQ,CAAC,OAAO;4CACjC,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,aAAa,SAAS,CAAC,MAAM,CAAC,IAAI,kBACjC,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO;;;;;;;;;;;;8CAGxF,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACE,GAAG,aAAa,QAAQ,CAAC,QAAQ;4CAClC,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,aAAa,SAAS,CAAC,MAAM,CAAC,KAAK,kBAClC,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO;;;;;;;;;;;;8CAGzF,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACE,GAAG,aAAa,QAAQ,CAAC,WAAW;4CACrC,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,aAAa,SAAS,CAAC,MAAM,CAAC,QAAQ,kBACrC,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAG5F,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAA0C;;;;;;sDAGrF,6LAAC;4CACE,GAAG,aAAa,QAAQ,CAAC,kBAAkB;4CAC5C,MAAK;4CACL,cAAa;4CACb,WAAU;4CACV,aAAY;;;;;;wCAEb,aAAa,SAAS,CAAC,MAAM,CAAC,eAAe,kBAC5C,6LAAC;4CAAE,WAAU;sDAA6B,aAAa,SAAS,CAAC,MAAM,CAAC,eAAe,CAAC,OAAO;;;;;;;;;;;;;;;;;;wBAKpG,yBACC,6LAAC;4BAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,QAAQ,CAAC,gBACb,uDACA,gDACJ;sCACC;;;;;;sCAIL,6LAAC;sCACC,cAAA,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnD;GApPwB;;QAMJ,iKAAA,CAAA,UAAO;QAIJ,iKAAA,CAAA,UAAO;;;KAVN", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/worker/ScaffoldingForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { createSupabaseClient } from '@/lib/supabase'\n\nconst scaffoldingSchema = z.object({\n  system_line: z.string().min(1, 'System line is required'),\n  short_iso_number: z.string().min(1, 'Short ISO number is required'),\n  start_date: z.string().min(1, 'Start date is required'),\n  finish_date: z.string().min(1, 'Finish date is required'),\n  status: z.enum(['ongoing', 'finished'], {\n    required_error: 'Status is required'\n  })\n}).refine((data) => {\n  const startDate = new Date(data.start_date)\n  const finishDate = new Date(data.finish_date)\n  return finishDate >= startDate\n}, {\n  message: 'Finish date must be after or equal to start date',\n  path: ['finish_date']\n})\n\ntype ScaffoldingFormData = z.infer<typeof scaffoldingSchema>\n\nexport default function ScaffoldingForm() {\n  const { user } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  const supabase = createSupabaseClient()\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: { errors }\n  } = useForm<ScaffoldingFormData>({\n    resolver: zodResolver(scaffoldingSchema)\n  })\n\n  const onSubmit = async (data: ScaffoldingFormData) => {\n    if (!user) return\n\n    setLoading(true)\n    setMessage('')\n\n    try {\n      const { error } = await supabase\n        .from('scaffolding_reports')\n        .insert({\n          user_id: user.id,\n          system_line: data.system_line,\n          short_iso_number: data.short_iso_number,\n          start_date: data.start_date,\n          finish_date: data.finish_date,\n          status: data.status\n        })\n\n      if (error) {\n        setMessage('Error submitting report: ' + error.message)\n      } else {\n        setMessage('Report submitted successfully!')\n        reset()\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n          Submit Scaffolding Report\n        </h2>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"system_line\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              System Line\n            </label>\n            <input\n              {...register('system_line')}\n              type=\"text\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Enter system line\"\n            />\n            {errors.system_line && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.system_line.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label htmlFor=\"short_iso_number\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Short ISO Number\n            </label>\n            <input\n              {...register('short_iso_number')}\n              type=\"text\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Enter short ISO number\"\n            />\n            {errors.short_iso_number && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.short_iso_number.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label htmlFor=\"start_date\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Start Date\n            </label>\n            <input\n              {...register('start_date')}\n              type=\"date\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            />\n            {errors.start_date && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.start_date.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label htmlFor=\"finish_date\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Finish Date\n            </label>\n            <input\n              {...register('finish_date')}\n              type=\"date\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            />\n            {errors.finish_date && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.finish_date.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label htmlFor=\"status\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Status\n            </label>\n            <select\n              {...register('status')}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"\">Select status</option>\n              <option value=\"ongoing\">Ongoing</option>\n              <option value=\"finished\">Finished</option>\n            </select>\n            {errors.status && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.status.message}</p>\n            )}\n          </div>\n\n          {message && (\n            <div className={`text-sm text-center p-3 rounded-md ${\n              message.includes('successfully') \n                ? 'bg-green-50 text-green-700 border border-green-200' \n                : 'bg-red-50 text-red-700 border border-red-200'\n            }`}>\n              {message}\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium\"\n          >\n            {loading ? 'Submitting...' : 'Submit Report'}\n          </button>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,oBAAoB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,kBAAkB,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,YAAY,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC9B,aAAa,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,QAAQ,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;KAAW,EAAE;QACtC,gBAAgB;IAClB;AACF,GAAG,MAAM,CAAC,CAAC;IACT,MAAM,YAAY,IAAI,KAAK,KAAK,UAAU;IAC1C,MAAM,aAAa,IAAI,KAAK,KAAK,WAAW;IAC5C,OAAO,cAAc;AACvB,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAc;AACvB;AAIe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAuB;QAC/B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,WAAW;QAEX,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,uBACL,MAAM,CAAC;gBACN,SAAS,KAAK,EAAE;gBAChB,aAAa,KAAK,WAAW;gBAC7B,kBAAkB,KAAK,gBAAgB;gBACvC,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;gBAC7B,QAAQ,KAAK,MAAM;YACrB;YAEF,IAAI,OAAO;gBACT,WAAW,8BAA8B,MAAM,OAAO;YACxD,OAAO;gBACL,WAAW;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAIzD,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACE,GAAG,SAAS,cAAc;oCAC3B,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;sCAIxE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAmB,WAAU;8CAA+C;;;;;;8CAG3F,6LAAC;oCACE,GAAG,SAAS,mBAAmB;oCAChC,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,gBAAgB,kBACtB,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,gBAAgB,CAAC,OAAO;;;;;;;;;;;;sCAI7E,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAa,WAAU;8CAA+C;;;;;;8CAGrF,6LAAC;oCACE,GAAG,SAAS,aAAa;oCAC1B,MAAK;oCACL,WAAU;;;;;;gCAEX,OAAO,UAAU,kBAChB,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;sCAIvE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAA+C;;;;;;8CAGtF,6LAAC;oCACE,GAAG,SAAS,cAAc;oCAC3B,MAAK;oCACL,WAAU;;;;;;gCAEX,OAAO,WAAW,kBACjB,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;sCAIxE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAS,WAAU;8CAA+C;;;;;;8CAGjF,6LAAC;oCACE,GAAG,SAAS,SAAS;oCACtB,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAG;;;;;;sDACjB,6LAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6LAAC;4CAAO,OAAM;sDAAW;;;;;;;;;;;;gCAE1B,OAAO,MAAM,kBACZ,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;wBAIlE,yBACC,6LAAC;4BAAI,WAAW,CAAC,mCAAmC,EAClD,QAAQ,QAAQ,CAAC,kBACb,uDACA,gDACJ;sCACC;;;;;;sCAIL,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAtJwB;;QACL,kIAAA,CAAA,UAAO;QAUpB,iKAAA,CAAA,UAAO;;;KAXW", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/profile/ProfileForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { createSupabaseClient } from '@/lib/supabase'\n\nconst profileSchema = z.object({\n  name: z.string().min(1, 'Name is required'),\n  email: z.string().email('Invalid email address')\n})\n\ntype ProfileFormData = z.infer<typeof profileSchema>\n\nexport default function ProfileForm() {\n  const { user, profile, refreshProfile } = useAuth()\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  const supabase = createSupabaseClient()\n\n  const {\n    register,\n    handleSubmit,\n    setValue,\n    formState: { errors }\n  } = useForm<ProfileFormData>({\n    resolver: zodResolver(profileSchema)\n  })\n\n  useEffect(() => {\n    if (profile) {\n      setValue('name', profile.name)\n      setValue('email', profile.email)\n    }\n  }, [profile, setValue])\n\n  const onSubmit = async (data: ProfileFormData) => {\n    if (!user || !profile) return\n\n    setLoading(true)\n    setMessage('')\n\n    try {\n      // Update profile in database\n      const { error: profileError } = await supabase\n        .from('profiles')\n        .update({\n          name: data.name,\n          email: data.email\n        })\n        .eq('id', user.id)\n\n      if (profileError) {\n        setMessage('Error updating profile: ' + profileError.message)\n        return\n      }\n\n      // Update email in auth if it changed\n      if (data.email !== profile.email) {\n        const { error: emailError } = await supabase.auth.updateUser({\n          email: data.email\n        })\n\n        if (emailError) {\n          setMessage('Profile updated but email change failed: ' + emailError.message)\n          return\n        }\n      }\n\n      setMessage('Profile updated successfully!')\n      await refreshProfile()\n    } catch (error) {\n      setMessage('An unexpected error occurred')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (!profile) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-lg\">Loading profile...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-md mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-6\">\n          Edit Profile\n        </h2>\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Full Name\n            </label>\n            <input\n              {...register('name')}\n              type=\"text\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Enter your full name\"\n            />\n            {errors.name && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Email Address\n            </label>\n            <input\n              {...register('email')}\n              type=\"email\"\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              placeholder=\"Enter your email address\"\n            />\n            {errors.email && (\n              <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n              Role\n            </label>\n            <div className=\"px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-700\">\n              {profile.role === 'manager' ? 'Manager' : 'Worker'}\n            </div>\n            <p className=\"mt-1 text-sm text-gray-500\">\n              Role cannot be changed. Contact an administrator if needed.\n            </p>\n          </div>\n\n          {message && (\n            <div className={`text-sm p-3 rounded-md ${\n              message.includes('successfully') \n                ? 'bg-green-50 text-green-700 border border-green-200' \n                : 'bg-red-50 text-red-700 border border-red-200'\n            }`}>\n              {message}\n            </div>\n          )}\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium\"\n          >\n            {loading ? 'Updating...' : 'Update Profile'}\n          </button>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,gBAAgB,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAIe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,SAAS;gBACX,SAAS,QAAQ,QAAQ,IAAI;gBAC7B,SAAS,SAAS,QAAQ,KAAK;YACjC;QACF;gCAAG;QAAC;QAAS;KAAS;IAEtB,MAAM,WAAW,OAAO;QACtB,IAAI,CAAC,QAAQ,CAAC,SAAS;QAEvB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,6BAA6B;YAC7B,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;YACnB,GACC,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,cAAc;gBAChB,WAAW,6BAA6B,aAAa,OAAO;gBAC5D;YACF;YAEA,qCAAqC;YACrC,IAAI,KAAK,KAAK,KAAK,QAAQ,KAAK,EAAE;gBAChC,MAAM,EAAE,OAAO,UAAU,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;oBAC3D,OAAO,KAAK,KAAK;gBACnB;gBAEA,IAAI,YAAY;oBACd,WAAW,8CAA8C,WAAW,OAAO;oBAC3E;gBACF;YACF;YAEA,WAAW;YACX,MAAM;QACR,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAIzD,6LAAC;oBAAK,UAAU,aAAa;oBAAW,WAAU;;sCAChD,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAO,WAAU;8CAA+C;;;;;;8CAG/E,6LAAC;oCACE,GAAG,SAAS,OAAO;oCACpB,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,IAAI,kBACV,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;sCAIjE,6LAAC;;8CACC,6LAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,6LAAC;oCACE,GAAG,SAAS,QAAQ;oCACrB,MAAK;oCACL,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,KAAK,kBACX,6LAAC;oCAAE,WAAU;8CAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sCAIlE,6LAAC;;8CACC,6LAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI,KAAK,YAAY,YAAY;;;;;;8CAE5C,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;wBAK3C,yBACC,6LAAC;4BAAI,WAAW,CAAC,uBAAuB,EACtC,QAAQ,QAAQ,CAAC,kBACb,uDACA,gDACJ;sCACC;;;;;;sCAIL,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,UAAU,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;AAMvC;GA/IwB;;QACoB,kIAAA,CAAA,UAAO;QAU7C,iKAAA,CAAA,UAAO;;;KAXW", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/worker/WorkerDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport ScaffoldingForm from './ScaffoldingForm'\nimport ProfileForm from '../profile/ProfileForm'\n\nexport default function WorkerDashboard() {\n  const { profile, signOut } = useAuth()\n  const [activeTab, setActiveTab] = useState<'form' | 'profile'>('form')\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"px-4 py-3\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-lg font-semibold text-gray-900\">\n                Scaffolding Management\n              </h1>\n              <p className=\"text-sm text-gray-600\">Welcome, {profile?.name}</p>\n            </div>\n            <button\n              onClick={signOut}\n              className=\"text-sm text-red-600 hover:text-red-700 font-medium\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white border-b\">\n        <div className=\"px-4\">\n          <nav className=\"flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('form')}\n              className={`py-3 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'form'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Submit Report\n            </button>\n            <button\n              onClick={() => setActiveTab('profile')}\n              className={`py-3 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'profile'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Profile\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-4\">\n        {activeTab === 'form' && <ScaffoldingForm />}\n        {activeTab === 'profile' && <ProfileForm />}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAE/D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDAGpD,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAU,SAAS;;;;;;;;;;;;;0CAE1D,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,SACV,kCACA,8EACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;0CACH;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,wBAAU,6LAAC,kJAAA,CAAA,UAAe;;;;;oBACxC,cAAc,2BAAa,6LAAC,+IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIhD;GA7DwB;;QACO,kIAAA,CAAA,UAAO;;;KADd", "debugId": null}}, {"offset": {"line": 1254, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/manager/SubmissionsList.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport * as XLSX from 'xlsx'\n\ninterface ScaffoldingReport {\n  id: string\n  user_id: string\n  system_line: string\n  short_iso_number: string\n  start_date: string\n  finish_date: string\n  status: 'ongoing' | 'finished'\n  created_at: string\n  profiles: {\n    name: string\n    email: string\n  }\n}\n\nexport default function SubmissionsList() {\n  const [reports, setReports] = useState<ScaffoldingReport[]>([])\n  const [loading, setLoading] = useState(true)\n  const [exporting, setExporting] = useState(false)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    fetchReports()\n  }, [])\n\n  const fetchReports = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('scaffolding_reports')\n        .select(`\n          *,\n          profiles (\n            name,\n            email\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching reports:', error)\n      } else {\n        setReports(data || [])\n      }\n    } catch (error) {\n      console.error('Error fetching reports:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const exportToExcel = async () => {\n    setExporting(true)\n    try {\n      // Prepare data for Excel export\n      const exportData = reports.map(report => ({\n        'Worker Name': report.profiles?.name || 'Unknown',\n        'Worker Email': report.profiles?.email || 'Unknown',\n        'System Line': report.system_line,\n        'Short ISO Number': report.short_iso_number,\n        'Start Date': report.start_date,\n        'Finish Date': report.finish_date,\n        'Status': report.status,\n        'Submitted At': new Date(report.created_at).toLocaleDateString()\n      }))\n\n      // Create workbook and worksheet\n      const wb = XLSX.utils.book_new()\n      const ws = XLSX.utils.json_to_sheet(exportData)\n\n      // Add worksheet to workbook\n      XLSX.utils.book_append_sheet(wb, ws, 'Scaffolding Reports')\n\n      // Generate filename with current date\n      const filename = `scaffolding_reports_${new Date().toISOString().split('T')[0]}.xlsx`\n\n      // Save file\n      XLSX.writeFile(wb, filename)\n    } catch (error) {\n      console.error('Error exporting to Excel:', error)\n    } finally {\n      setExporting(false)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString()\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-lg\">Loading submissions...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto\">\n      <div className=\"bg-white rounded-lg shadow-sm border\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-xl font-semibold text-gray-900\">\n              Scaffolding Reports ({reports.length})\n            </h2>\n            <button\n              onClick={exportToExcel}\n              disabled={exporting || reports.length === 0}\n              className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium text-sm\"\n            >\n              {exporting ? 'Exporting...' : 'Export to Excel'}\n            </button>\n          </div>\n        </div>\n\n        {reports.length === 0 ? (\n          <div className=\"px-6 py-8 text-center text-gray-500\">\n            No submissions found\n          </div>\n        ) : (\n          <div className=\"table-responsive\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Worker\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    System Line\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    ISO Number\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Dates\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Status\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Submitted\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {reports.map((report) => (\n                  <tr key={report.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {report.profiles?.name || 'Unknown'}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {report.profiles?.email || 'Unknown'}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {report.system_line}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {report.short_iso_number}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      <div>\n                        <div>Start: {formatDate(report.start_date)}</div>\n                        <div>End: {formatDate(report.finish_date)}</div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        report.status === 'finished'\n                          ? 'bg-green-100 text-green-800'\n                          : 'bg-yellow-100 text-yellow-800'\n                      }`}>\n                        {report.status}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatDate(report.created_at)}\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAqBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,uBACL,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;YAC3C,OAAO;gBACL,WAAW,QAAQ,EAAE;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,aAAa;QACb,IAAI;YACF,gCAAgC;YAChC,MAAM,aAAa,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;oBACxC,eAAe,OAAO,QAAQ,EAAE,QAAQ;oBACxC,gBAAgB,OAAO,QAAQ,EAAE,SAAS;oBAC1C,eAAe,OAAO,WAAW;oBACjC,oBAAoB,OAAO,gBAAgB;oBAC3C,cAAc,OAAO,UAAU;oBAC/B,eAAe,OAAO,WAAW;oBACjC,UAAU,OAAO,MAAM;oBACvB,gBAAgB,IAAI,KAAK,OAAO,UAAU,EAAE,kBAAkB;gBAChE,CAAC;YAED,gCAAgC;YAChC,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,QAAQ;YAC9B,MAAM,KAAK,gIAAA,CAAA,QAAU,CAAC,aAAa,CAAC;YAEpC,4BAA4B;YAC5B,gIAAA,CAAA,QAAU,CAAC,iBAAiB,CAAC,IAAI,IAAI;YAErC,sCAAsC;YACtC,MAAM,WAAW,CAAC,oBAAoB,EAAE,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAErF,YAAY;YACZ,CAAA,GAAA,gIAAA,CAAA,YAAc,AAAD,EAAE,IAAI;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAsC;oCAC5B,QAAQ,MAAM;oCAAC;;;;;;;0CAEvC,6LAAC;gCACC,SAAS;gCACT,UAAU,aAAa,QAAQ,MAAM,KAAK;gCAC1C,WAAU;0CAET,YAAY,iBAAiB;;;;;;;;;;;;;;;;;gBAKnC,QAAQ,MAAM,KAAK,kBAClB,6LAAC;oBAAI,WAAU;8BAAsC;;;;;yCAIrD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAM,WAAU;;0CACf,6LAAC;gCAAM,WAAU;0CACf,cAAA,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;sDAG/F,6LAAC;4CAAG,WAAU;sDAAiF;;;;;;;;;;;;;;;;;0CAKnG,6LAAC;gCAAM,WAAU;0CACd,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,EAAE,QAAQ;;;;;;sEAE5B,6LAAC;4DAAI,WAAU;sEACZ,OAAO,QAAQ,EAAE,SAAS;;;;;;;;;;;;;;;;;0DAIjC,6LAAC;gDAAG,WAAU;0DACX,OAAO,WAAW;;;;;;0DAErB,6LAAC;gDAAG,WAAU;0DACX,OAAO,gBAAgB;;;;;;0DAE1B,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;;sEACC,6LAAC;;gEAAI;gEAAQ,WAAW,OAAO,UAAU;;;;;;;sEACzC,6LAAC;;gEAAI;gEAAM,WAAW,OAAO,WAAW;;;;;;;;;;;;;;;;;;0DAG5C,6LAAC;gDAAG,WAAU;0DACZ,cAAA,6LAAC;oDAAK,WAAW,CAAC,yDAAyD,EACzE,OAAO,MAAM,KAAK,aACd,gCACA,iCACJ;8DACC,OAAO,MAAM;;;;;;;;;;;0DAGlB,6LAAC;gDAAG,WAAU;0DACX,WAAW,OAAO,UAAU;;;;;;;uCAjCxB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CpC;GA9KwB;KAAA", "debugId": null}}, {"offset": {"line": 1624, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/manager/UserManagement.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport { z } from 'zod'\nimport { createSupabaseClient } from '@/lib/supabase'\n\ninterface Profile {\n  id: string\n  email: string\n  name: string\n  role: 'manager' | 'worker'\n  created_at: string\n}\n\nconst inviteSchema = z.object({\n  email: z.string().email('Invalid email address'),\n  name: z.string().min(1, 'Name is required'),\n  role: z.enum(['manager', 'worker'], {\n    required_error: 'Role is required'\n  })\n})\n\ntype InviteFormData = z.infer<typeof inviteSchema>\n\nexport default function UserManagement() {\n  const [users, setUsers] = useState<Profile[]>([])\n  const [loading, setLoading] = useState(true)\n  const [inviting, setInviting] = useState(false)\n  const [message, setMessage] = useState('')\n  const supabase = createSupabaseClient()\n\n  const {\n    register,\n    handleSubmit,\n    reset,\n    formState: { errors }\n  } = useForm<InviteFormData>({\n    resolver: zodResolver(inviteSchema)\n  })\n\n  useEffect(() => {\n    fetchUsers()\n  }, [])\n\n  const fetchUsers = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .order('created_at', { ascending: false })\n\n      if (error) {\n        console.error('Error fetching users:', error)\n      } else {\n        setUsers(data || [])\n      }\n    } catch (error) {\n      console.error('Error fetching users:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const onInvite = async (data: InviteFormData) => {\n    setInviting(true)\n    setMessage('')\n\n    try {\n      // Send invitation email using Supabase Auth\n      const { error } = await supabase.auth.admin.inviteUserByEmail(data.email, {\n        data: {\n          name: data.name,\n          role: data.role\n        }\n      })\n\n      if (error) {\n        setMessage('Error sending invitation: ' + error.message)\n      } else {\n        setMessage('Invitation sent successfully!')\n        reset()\n        fetchUsers() // Refresh the user list\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred')\n    } finally {\n      setInviting(false)\n    }\n  }\n\n  const deleteUser = async (userId: string) => {\n    if (!confirm('Are you sure you want to delete this user?')) {\n      return\n    }\n\n    try {\n      const { error } = await supabase.auth.admin.deleteUser(userId)\n\n      if (error) {\n        setMessage('Error deleting user: ' + error.message)\n      } else {\n        setMessage('User deleted successfully!')\n        fetchUsers() // Refresh the user list\n      }\n    } catch (error) {\n      setMessage('An unexpected error occurred')\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString()\n  }\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-lg\">Loading users...</div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      {/* Invite User Form */}\n      <div className=\"bg-white rounded-lg shadow-sm border p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n          Invite New User\n        </h2>\n\n        <form onSubmit={handleSubmit(onInvite)} className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Email\n              </label>\n              <input\n                {...register('email')}\n                type=\"email\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"<EMAIL>\"\n              />\n              {errors.email && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Name\n              </label>\n              <input\n                {...register('name')}\n                type=\"text\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n                placeholder=\"Full name\"\n              />\n              {errors.name && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.name.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"role\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Role\n              </label>\n              <select\n                {...register('role')}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"\">Select role</option>\n                <option value=\"worker\">Worker</option>\n                <option value=\"manager\">Manager</option>\n              </select>\n              {errors.role && (\n                <p className=\"mt-1 text-sm text-red-600\">{errors.role.message}</p>\n              )}\n            </div>\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={inviting}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium\"\n          >\n            {inviting ? 'Sending...' : 'Send Invitation'}\n          </button>\n        </form>\n\n        {message && (\n          <div className={`mt-4 text-sm p-3 rounded-md ${\n            message.includes('successfully') \n              ? 'bg-green-50 text-green-700 border border-green-200' \n              : 'bg-red-50 text-red-700 border border-red-200'\n          }`}>\n            {message}\n          </div>\n        )}\n      </div>\n\n      {/* Users List */}\n      <div className=\"bg-white rounded-lg shadow-sm border\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-gray-900\">\n            Users ({users.length})\n          </h2>\n        </div>\n\n        {users.length === 0 ? (\n          <div className=\"px-6 py-8 text-center text-gray-500\">\n            No users found\n          </div>\n        ) : (\n          <div className=\"table-responsive\">\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    User\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Role\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Created\n                  </th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                    Actions\n                  </th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {users.map((user) => (\n                  <tr key={user.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div>\n                        <div className=\"text-sm font-medium text-gray-900\">\n                          {user.name}\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {user.email}\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${\n                        user.role === 'manager'\n                          ? 'bg-purple-100 text-purple-800'\n                          : 'bg-blue-100 text-blue-800'\n                      }`}>\n                        {user.role}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {formatDate(user.created_at)}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                      <button\n                        onClick={() => deleteUser(user.id)}\n                        className=\"text-red-600 hover:text-red-900\"\n                      >\n                        Delete\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAgBA,MAAM,eAAe,qKAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5B,OAAO,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,MAAM,qKAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,MAAM,qKAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAW;KAAS,EAAE;QAClC,gBAAgB;IAClB;AACF;AAIe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAkB;QAC1B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,yBAAyB;YACzC,OAAO;gBACL,SAAS,QAAQ,EAAE;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,YAAY;QACZ,WAAW;QAEX,IAAI;YACF,4CAA4C;YAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,iBAAiB,CAAC,KAAK,KAAK,EAAE;gBACxE,MAAM;oBACJ,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;YAEA,IAAI,OAAO;gBACT,WAAW,+BAA+B,MAAM,OAAO;YACzD,OAAO;gBACL,WAAW;gBACX;gBACA,aAAa,wBAAwB;;YACvC;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,QAAQ,+CAA+C;YAC1D;QACF;QAEA,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;YAEvD,IAAI,OAAO;gBACT,WAAW,0BAA0B,MAAM,OAAO;YACpD,OAAO;gBACL,WAAW;gBACX,aAAa,wBAAwB;;YACvC;QACF,EAAE,OAAO,OAAO;YACd,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB;IAChD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAIzD,6LAAC;wBAAK,UAAU,aAAa;wBAAW,WAAU;;0CAChD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAQ,WAAU;0DAA+C;;;;;;0DAGhF,6LAAC;gDACE,GAAG,SAAS,QAAQ;gDACrB,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;4CAEb,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kDAIlE,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,6LAAC;gDACE,GAAG,SAAS,OAAO;gDACpB,MAAK;gDACL,WAAU;gDACV,aAAY;;;;;;4CAEb,OAAO,IAAI,kBACV,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;kDAIjE,6LAAC;;0DACC,6LAAC;gDAAM,SAAQ;gDAAO,WAAU;0DAA+C;;;;;;0DAG/E,6LAAC;gDACE,GAAG,SAAS,OAAO;gDACpB,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAU;;;;;;;;;;;;4CAEzB,OAAO,IAAI,kBACV,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;;;;;;0CAKnE,6LAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,WAAW,eAAe;;;;;;;;;;;;oBAI9B,yBACC,6LAAC;wBAAI,WAAW,CAAC,4BAA4B,EAC3C,QAAQ,QAAQ,CAAC,kBACb,uDACA,gDACJ;kCACC;;;;;;;;;;;;0BAMP,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAG,WAAU;;gCAAsC;gCAC1C,MAAM,MAAM;gCAAC;;;;;;;;;;;;oBAIxB,MAAM,MAAM,KAAK,kBAChB,6LAAC;wBAAI,WAAU;kCAAsC;;;;;6CAIrD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4CAAiB,WAAU;;8DAC1B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EACZ,KAAK,IAAI;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;0EACZ,KAAK,KAAK;;;;;;;;;;;;;;;;;8DAIjB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,CAAC,yDAAyD,EACzE,KAAK,IAAI,KAAK,YACV,kCACA,6BACJ;kEACC,KAAK,IAAI;;;;;;;;;;;8DAGd,6LAAC;oDAAG,WAAU;8DACX,WAAW,KAAK,UAAU;;;;;;8DAE7B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDACC,SAAS,IAAM,WAAW,KAAK,EAAE;wDACjC,WAAU;kEACX;;;;;;;;;;;;2CA3BI,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwClC;GAxPwB;;QAYlB,iKAAA,CAAA,UAAO;;;KAZW", "debugId": null}}, {"offset": {"line": 2138, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/components/manager/ManagerDashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useAuth } from '@/contexts/AuthContext'\nimport SubmissionsList from './SubmissionsList'\nimport UserManagement from './UserManagement'\nimport ProfileForm from '../profile/ProfileForm'\n\nexport default function ManagerDashboard() {\n  const { profile, signOut } = useAuth()\n  const [activeTab, setActiveTab] = useState<'submissions' | 'users' | 'profile'>('submissions')\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <div className=\"bg-white shadow-sm border-b\">\n        <div className=\"px-4 py-3\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-lg font-semibold text-gray-900\">\n                Manager Dashboard\n              </h1>\n              <p className=\"text-sm text-gray-600\">Welcome, {profile?.name}</p>\n            </div>\n            <button\n              onClick={signOut}\n              className=\"text-sm text-red-600 hover:text-red-700 font-medium\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation Tabs */}\n      <div className=\"bg-white border-b\">\n        <div className=\"px-4\">\n          <nav className=\"flex space-x-8\">\n            <button\n              onClick={() => setActiveTab('submissions')}\n              className={`py-3 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'submissions'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Submissions\n            </button>\n            <button\n              onClick={() => setActiveTab('users')}\n              className={`py-3 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'users'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Users\n            </button>\n            <button\n              onClick={() => setActiveTab('profile')}\n              className={`py-3 px-1 border-b-2 font-medium text-sm ${\n                activeTab === 'profile'\n                  ? 'border-blue-500 text-blue-600'\n                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n              }`}\n            >\n              Profile\n            </button>\n          </nav>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-4\">\n        {activeTab === 'submissions' && <SubmissionsList />}\n        {activeTab === 'users' && <UserManagement />}\n        {activeTab === 'profile' && <ProfileForm />}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAEhF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDAGpD,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAU,SAAS;;;;;;;;;;;;;0CAE1D,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,gBACV,kCACA,8EACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,UACV,kCACA,8EACJ;0CACH;;;;;;0CAGD,6LAAC;gCACC,SAAS,IAAM,aAAa;gCAC5B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,kCACA,8EACJ;0CACH;;;;;;;;;;;;;;;;;;;;;;0BAQP,6LAAC;gBAAI,WAAU;;oBACZ,cAAc,+BAAiB,6LAAC,mJAAA,CAAA,UAAe;;;;;oBAC/C,cAAc,yBAAW,6LAAC,kJAAA,CAAA,UAAc;;;;;oBACxC,cAAc,2BAAa,6LAAC,+IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;AAIhD;GAxEwB;;QACO,kIAAA,CAAA,UAAO;;;KADd", "debugId": null}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport AuthForm from '@/components/auth/AuthForm'\nimport WorkerDashboard from '@/components/worker/WorkerDashboard'\nimport ManagerDashboard from '@/components/manager/ManagerDashboard'\n\nexport default function Home() {\n  const { user, profile, loading } = useAuth()\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-lg\">Loading...</div>\n      </div>\n    )\n  }\n\n  if (!user || !profile) {\n    return <AuthForm />\n  }\n\n  if (profile.role === 'manager') {\n    return <ManagerDashboard />\n  }\n\n  return <WorkerDashboard />\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEzC,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,QAAQ,CAAC,SAAS;QACrB,qBAAO,6LAAC,yIAAA,CAAA,UAAQ;;;;;IAClB;IAEA,IAAI,QAAQ,IAAI,KAAK,WAAW;QAC9B,qBAAO,6LAAC,oJAAA,CAAA,UAAgB;;;;;IAC1B;IAEA,qBAAO,6LAAC,kJAAA,CAAA,UAAe;;;;;AACzB;GApBwB;;QACa,kIAAA,CAAA,UAAO;;;KADpB", "debugId": null}}]}