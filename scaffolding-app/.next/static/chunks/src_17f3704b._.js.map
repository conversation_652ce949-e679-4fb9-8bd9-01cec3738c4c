{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport function createSupabaseClient() {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// For server-side operations\nexport function createSupabaseServerClient() {\n  return createClient(\n    supabaseUrl,\n    process.env.SUPABASE_SERVICE_ROLE_KEY!,\n    {\n      auth: {\n        autoRefreshToken: false,\n        persistSession: false\n      }\n    }\n  )\n}\n\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          name: string\n          role: 'manager' | 'worker'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          name: string\n          role: 'manager' | 'worker'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          name?: string\n          role?: 'manager' | 'worker'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      scaffolding_reports: {\n        Row: {\n          id: string\n          user_id: string\n          system_line: string\n          short_iso_number: string\n          start_date: string\n          finish_date: string\n          status: 'ongoing' | 'finished'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          system_line: string\n          short_iso_number: string\n          start_date: string\n          finish_date: string\n          status: 'ongoing' | 'finished'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          system_line?: string\n          short_iso_number?: string\n          start_date?: string\n          finish_date?: string\n          status?: 'ongoing' | 'finished'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAE3C,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C;AAGO,SAAS;IACd,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAChB,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,EACrC;QACE,MAAM;YACJ,kBAAkB;YAClB,gBAAgB;QAClB;IACF;AAEJ", "debugId": null}}, {"offset": {"line": 41, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/coding/tdarbas/scaffolding-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createSupabaseClient } from '@/lib/supabase'\n\ninterface Profile {\n  id: string\n  email: string\n  name: string\n  role: 'manager' | 'worker'\n  created_at: string\n  updated_at: string\n}\n\ninterface AuthContextType {\n  user: User | null\n  profile: Profile | null\n  loading: boolean\n  signOut: () => Promise<void>\n  refreshProfile: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [profile, setProfile] = useState<Profile | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  const fetchProfile = async (userId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('profiles')\n        .select('*')\n        .eq('id', userId)\n        .single()\n\n      if (error) {\n        console.error('Error fetching profile:', error)\n        return null\n      }\n\n      return data\n    } catch (error) {\n      console.error('Error fetching profile:', error)\n      return null\n    }\n  }\n\n  const refreshProfile = async () => {\n    if (user) {\n      const profileData = await fetchProfile(user.id)\n      setProfile(profileData)\n    }\n  }\n\n  useEffect(() => {\n    const getSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setUser(session?.user ?? null)\n      \n      if (session?.user) {\n        const profileData = await fetchProfile(session.user.id)\n        setProfile(profileData)\n      }\n      \n      setLoading(false)\n    }\n\n    getSession()\n\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setUser(session?.user ?? null)\n        \n        if (session?.user) {\n          const profileData = await fetchProfile(session.user.id)\n          setProfile(profileData)\n        } else {\n          setProfile(null)\n        }\n        \n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n    setUser(null)\n    setProfile(null)\n  }\n\n  const value = {\n    user,\n    profile,\n    loading,\n    signOut,\n    refreshProfile\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAuBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QACT,MAAM;YAET,IAAI,OAAO;gBACT,QAAQ,KAAK,CAAC,2BAA2B;gBACzC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,MAAM;YACR,MAAM,cAAc,MAAM,aAAa,KAAK,EAAE;YAC9C,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;qDAAa;oBACjB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAC5D,QAAQ,SAAS,QAAQ;oBAEzB,IAAI,SAAS,MAAM;wBACjB,MAAM,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;wBACtD,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAEA;YAEA,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,QAAQ,SAAS,QAAQ;oBAEzB,IAAI,SAAS,MAAM;wBACjB,MAAM,cAAc,MAAM,aAAa,QAAQ,IAAI,CAAC,EAAE;wBACtD,WAAW;oBACb,OAAO;wBACL,WAAW;oBACb;oBAEA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;GArFgB;KAAA;AAuFT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}